{"version": 3, "file": "MessageFormatter.js", "sourceRoot": "", "sources": ["../../../src/components/chat/MessageFormatter.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAmBhC,MAAM,CAAC,MAAM,gBAAgB,GAAoC,CAAC,EAChE,YAAY,EACZ,OAAO,EACP,cAAc,GAAG,IAAI,EACrB,sBAAsB,GAAG,IAAI,EAC7B,mBAAmB,GAAG,IAAI,EAC1B,QAAQ,GAAG,EAAE,GACd,EAAE,EAAE;IACH,MAAM,aAAa,GAAG,CAAC,IAAY,EAAsB,EAAE;QACzD,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QAC3C,CAAC;QAED,MAAM,QAAQ,GAAuB,EAAE,CAAC;QACxC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,iBAAiB,GAAG,EAAE,CAAC;QAE3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEtB,cAAc;YACd,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC3B,IAAI,WAAW,EAAE,CAAC;oBAChB,WAAW,GAAG,KAAK,CAAC;oBACpB,iBAAiB,GAAG,EAAE,CAAC;gBACzB,CAAC;qBAAM,CAAC;oBACN,WAAW,GAAG,IAAI,CAAC;oBACnB,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAC/C,CAAC;gBACD,SAAS;YACX,CAAC;YAED,IAAI,WAAW,EAAE,CAAC;gBAChB,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,IAAI;oBACb,QAAQ,EAAE,iBAAiB;iBAC5B,CAAC,CAAC;gBACH,SAAS;YACX,CAAC;YAED,UAAU;YACV,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACzB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC;gBACjD,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;gBACnD,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,aAAa;oBACtB,KAAK;iBACN,CAAC,CAAC;gBACH,SAAS;YACX,CAAC;YAED,QAAQ;YACR,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC9B,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;gBACpD,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,WAAW;iBACrB,CAAC,CAAC;gBACH,SAAS;YACX,CAAC;YAED,0BAA0B;YAC1B,MAAM,cAAc,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;YACnD,QAAQ,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;YAEjC,sCAAsC;YACtC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzB,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC,CAAC;IAEF,MAAM,qBAAqB,GAAG,CAAC,IAAY,EAAsB,EAAE;QACjE,MAAM,QAAQ,GAAuB,EAAE,CAAC;QACxC,IAAI,WAAW,GAAG,IAAI,CAAC;QAEvB,+CAA+C;QAC/C,MAAM,QAAQ,GAAG;YACf,EAAE,IAAI,EAAE,MAAe,EAAE,KAAK,EAAE,YAAY,EAAE;YAC9C,EAAE,IAAI,EAAE,MAAe,EAAE,KAAK,EAAE,kBAAkB,EAAE;YACpD,EAAE,IAAI,EAAE,QAAiB,EAAE,KAAK,EAAE,cAAc,EAAE;YAClD,EAAE,IAAI,EAAE,MAAe,EAAE,KAAK,EAAE,0BAA0B,EAAE;SAC7D,CAAC;QAEF,MAAM,OAAO,GAAyG,EAAE,CAAC;QAEzH,mBAAmB;QACnB,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,IAAI,KAAK,CAAC;YACV,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBACnD,OAAO,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,KAAK,EAAE,KAAK,CAAC,KAAK;oBAClB,GAAG,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM;oBAClC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;oBACjB,GAAG,EAAE,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;iBACpD,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,2BAA2B;QAC3B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;QAE1C,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACtB,4BAA4B;YAC5B,IAAI,KAAK,CAAC,KAAK,GAAG,OAAO,EAAE,CAAC;gBAC1B,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBACxD,IAAI,UAAU,EAAE,CAAC;oBACf,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC,CAAC;gBACvD,CAAC;YACH,CAAC;YAED,4BAA4B;YAC5B,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAC;YAEH,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAC1B,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YAC9C,IAAI,aAAa,EAAE,CAAC;gBAClB,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;QAED,gDAAgD;QAChD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QACjD,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,CAAC,IAAY,EAAU,EAAE;QAC3C,IAAI,CAAC,mBAAmB;YAAE,OAAO,IAAI,CAAC;QAEtC,MAAM,QAAQ,GAAG,sBAAsB,CAAC;QACxC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,GAAG,KAAK,GAAG,GAAG,CAAC,CAAC;IAC7D,CAAC,CAAC;IAEF,MAAM,QAAQ,GAAG,CAAC,IAAY,EAAE,KAAa,EAAY,EAAE;QACzD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,IAAI,WAAW,GAAG,EAAE,CAAC;QAErB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,IAAI,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,EAAE,CAAC;gBAClD,WAAW,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACN,IAAI,WAAW;oBAAE,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACzC,WAAW,GAAG,IAAI,CAAC;YACrB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,WAAW;YAAE,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACzC,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CAAC,OAAyB,EAAE,KAAa,EAAmB,EAAE;QAClF,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACrB,KAAK,MAAM;gBACT,OAAO,KAAC,IAAI,cAAc,OAAO,CAAC,OAAO,IAAvB,KAAK,CAA0B,CAAC;YAEpD,KAAK,MAAM;gBACT,OAAO,KAAC,IAAI,cAAc,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,IAA7C,KAAK,CAAgD,CAAC;YAE1E,KAAK,QAAQ;gBACX,OAAO,KAAC,IAAI,cAAc,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,IAA/C,KAAK,CAAkD,CAAC;YAE5E,KAAK,MAAM;gBACT,OAAO,KAAC,IAAI,cAAc,YAAY,CAAC,MAAM,CAAC,KAAK,OAAO,CAAC,OAAO,IAAI,CAAC,IAArD,KAAK,CAAwD,CAAC;YAElF,KAAK,WAAW;gBACd,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,MAAM,CAAC;gBAC5C,OAAO,CACL,KAAC,GAAG,IAAa,UAAU,EAAE,CAAC,EAAE,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,MAAM,YACrE,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,CAAC,aACnC,QAAQ,IAAI,CACX,KAAC,IAAI,cAAE,YAAY,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,CAAC,GAAQ,CACpD,EACD,KAAC,IAAI,cAAE,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,GAAQ,IAC/C,IANE,KAAK,CAOT,CACP,CAAC;YAEJ,KAAK,QAAQ;gBACX,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;oBAC7C,OAAO,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;wBAC9C,YAAY,CAAC,MAAM,CAAC;gBACvC,MAAM,YAAY,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;gBACpD,OAAO,CACL,KAAC,IAAI,cACF,WAAW,CAAC,GAAG,YAAY,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC,IADzC,KAAK,CAET,CACR,CAAC;YAEJ,KAAK,MAAM;gBACT,OAAO,CACL,KAAC,GAAG,IAAa,UAAU,EAAE,CAAC,YAC5B,MAAC,IAAI,eAAE,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,OAAG,OAAO,CAAC,OAAO,IAAQ,IADjD,KAAK,CAET,CACP,CAAC;YAEJ,KAAK,MAAM;gBACT,OAAO,KAAC,IAAI,cAAc,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAA1C,KAAK,CAA6C,CAAC;YAEvE;gBACE,OAAO,KAAC,IAAI,cAAc,OAAO,CAAC,OAAO,IAAvB,KAAK,CAA0B,CAAC;QACtD,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;IAC9E,MAAM,QAAQ,GAAG,aAAa,CAAC,gBAAgB,CAAC,CAAC;IAEjD,OAAO,CACL,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,KAAK,EAAE,QAAQ,YACxC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,GAC5D,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,iCAAiC;AACjC,MAAM,CAAC,MAAM,aAAa,GAAuF,CAAC,KAAK,EAAE,EAAE,CAAC,CAC1H,KAAC,gBAAgB,OAAK,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,sBAAsB,EAAE,IAAI,GAAI,CACrF,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAA+G,CAAC,KAAK,EAAE,EAAE,CAAC,CACvJ,KAAC,gBAAgB,OAAK,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,sBAAsB,EAAE,KAAK,EAAE,mBAAmB,EAAE,KAAK,GAAI,CAClH,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAA4D,CAAC,KAAK,EAAE,EAAE,CAAC,CACnG,KAAC,gBAAgB,OAAK,KAAK,EAAE,cAAc,EAAE,IAAI,GAAI,CACtD,CAAC"}