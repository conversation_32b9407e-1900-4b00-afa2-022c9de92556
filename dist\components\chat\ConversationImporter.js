import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import * as fs from 'fs/promises';
import * as path from 'path';
export const ConversationImporter = ({ themeManager, isOpen, onClose, onImportComplete, }) => {
    const [fileName, setFileName] = useState('');
    const [isImporting, setIsImporting] = useState(false);
    const [importStatus, setImportStatus] = useState('');
    const [previewData, setPreviewData] = useState(null);
    const [importMode, setImportMode] = useState('input');
    useInput((input, key) => {
        if (!isOpen || isImporting)
            return;
        if (key.escape) {
            if (importMode === 'preview' || importMode === 'confirm') {
                setImportMode('input');
                setPreviewData(null);
                return;
            }
            onClose();
            return;
        }
        if (importMode === 'input') {
            if (key.return && fileName.trim()) {
                handlePreview();
                return;
            }
            if (key.backspace || key.delete) {
                setFileName(prev => prev.slice(0, -1));
                return;
            }
            if (input && input.length === 1 && !key.ctrl && !key.meta) {
                setFileName(prev => prev + input);
            }
        }
        if (importMode === 'confirm') {
            if (input === 'y' || input === 'Y') {
                handleImport();
                return;
            }
            if (input === 'n' || input === 'N') {
                setImportMode('input');
                setPreviewData(null);
                return;
            }
        }
    });
    const detectFileFormat = (filePath) => {
        const ext = path.extname(filePath).toLowerCase();
        switch (ext) {
            case '.json':
                return 'json';
            case '.csv':
                return 'csv';
            default:
                return 'unknown';
        }
    };
    const parseJSONFile = async (content) => {
        try {
            const data = JSON.parse(content);
            // Validate structure
            if (!data.messages || !Array.isArray(data.messages)) {
                throw new Error('Invalid JSON format: missing messages array');
            }
            return {
                exportedAt: data.exportedAt,
                messageCount: data.messageCount || data.messages.length,
                messages: data.messages,
            };
        }
        catch (error) {
            throw new Error(`JSON parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    };
    const parseCSVFile = async (content) => {
        try {
            const lines = content.split('\n').filter(line => line.trim());
            if (lines.length < 2) {
                throw new Error('CSV file must have at least a header and one data row');
            }
            const header = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
            const messages = [];
            for (let i = 1; i < lines.length; i++) {
                const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());
                const message = {};
                header.forEach((col, index) => {
                    if (values[index] !== undefined) {
                        switch (col.toLowerCase()) {
                            case 'role':
                                message.role = values[index];
                                break;
                            case 'content':
                                message.content = values[index];
                                break;
                            case 'timestamp':
                                message.timestamp = new Date(values[index]).getTime();
                                break;
                            case 'error':
                                if (values[index])
                                    message.error = values[index];
                                break;
                            case 'isstreaming':
                                message.isStreaming = values[index].toLowerCase() === 'true';
                                break;
                        }
                    }
                });
                if (message.role && message.content) {
                    messages.push(message);
                }
            }
            return {
                messageCount: messages.length,
                messages,
            };
        }
        catch (error) {
            throw new Error(`CSV parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    };
    const handlePreview = async () => {
        if (!fileName.trim())
            return;
        setIsImporting(true);
        setImportStatus('Reading file...');
        try {
            const filePath = path.resolve(process.cwd(), fileName);
            const content = await fs.readFile(filePath, 'utf-8');
            setImportStatus('Parsing content...');
            const format = detectFileFormat(filePath);
            let data;
            switch (format) {
                case 'json':
                    data = await parseJSONFile(content);
                    break;
                case 'csv':
                    data = await parseCSVFile(content);
                    break;
                default:
                    throw new Error('Unsupported file format. Please use .json or .csv files.');
            }
            setPreviewData(data);
            setImportMode('preview');
            setImportStatus('');
        }
        catch (error) {
            setImportStatus(`Preview failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            setTimeout(() => {
                setIsImporting(false);
                setImportStatus('');
            }, 3000);
        }
        finally {
            setIsImporting(false);
        }
    };
    const handleImport = async () => {
        if (!previewData)
            return;
        setIsImporting(true);
        setImportStatus('Importing messages...');
        try {
            const messages = previewData.messages.map((msg, index) => ({
                role: msg.role || 'user',
                content: msg.content || '',
                timestamp: msg.timestamp || Date.now() + index,
                error: msg.error,
                isStreaming: msg.isStreaming || false,
            }));
            setImportStatus(`Successfully imported ${messages.length} messages`);
            onImportComplete?.(messages);
            setTimeout(() => {
                onClose();
            }, 2000);
        }
        catch (error) {
            setImportStatus(`Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            setTimeout(() => {
                setIsImporting(false);
                setImportStatus('');
                setImportMode('input');
            }, 3000);
        }
    };
    if (!isOpen)
        return null;
    const renderFileInput = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary('Enter file path to import:') }), _jsx(Text, {}), _jsxs(Box, { marginLeft: 2, children: [_jsx(Text, { children: themeManager.secondary('File: ') }), _jsx(Text, { children: themeManager.primary(fileName || '...') })] }), _jsx(Text, {}), _jsxs(Box, { marginLeft: 2, children: [_jsx(Text, { children: themeManager.muted('Supported formats: .json, .csv') }), _jsx(Text, { children: themeManager.muted('Press Enter to preview, Esc to cancel') })] })] }));
    const renderPreview = () => {
        if (!previewData)
            return null;
        return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary('Import Preview:') }), _jsx(Text, {}), _jsxs(Box, { marginLeft: 2, flexDirection: "column", children: [_jsxs(Text, { children: [themeManager.secondary('File: '), themeManager.info(fileName)] }), _jsxs(Text, { children: [themeManager.secondary('Messages: '), themeManager.info(previewData.messageCount?.toString() || '0')] }), previewData.exportedAt && (_jsxs(Text, { children: [themeManager.secondary('Exported: '), themeManager.muted(new Date(previewData.exportedAt).toLocaleString())] }))] }), _jsx(Text, {}), _jsx(Text, { children: themeManager.secondary('Sample Messages:') }), previewData.messages.slice(0, 3).map((msg, index) => (_jsx(Box, { marginLeft: 2, marginTop: 1, borderStyle: "single", borderColor: "gray", padding: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsxs(Text, { children: [themeManager.accent(`${msg.role || 'unknown'}:`), " ", themeManager.muted(new Date(msg.timestamp || 0).toLocaleString())] }), _jsx(Text, { children: themeManager.primary((msg.content || '').substring(0, 100) + (msg.content?.length > 100 ? '...' : '')) })] }) }, index))), previewData.messages.length > 3 && (_jsx(Box, { marginLeft: 2, marginTop: 1, children: _jsx(Text, { children: themeManager.muted(`... and ${previewData.messages.length - 3} more messages`) }) })), _jsx(Text, {}), _jsx(Text, { children: themeManager.warning('Import these messages? [y/N]') })] }));
    };
    const renderImportProgress = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { children: themeManager.primary('Importing Conversation...') }), _jsx(Text, {}), _jsx(Box, { marginLeft: 2, children: _jsx(Text, { children: themeManager.info(importStatus) }) })] }));
    return (_jsx(Box, { flexDirection: "column", padding: 1, children: _jsx(Box, { borderStyle: "round", borderColor: "blue", padding: 1, children: _jsxs(Box, { flexDirection: "column", width: "100%", children: [_jsx(Text, { children: themeManager.primary('📥 Import Conversation') }), _jsx(Text, {}), isImporting ? (renderImportProgress()) : importMode === 'input' ? (renderFileInput()) : importMode === 'preview' ? (renderPreview()) : null] }) }) }));
};
//# sourceMappingURL=ConversationImporter.js.map