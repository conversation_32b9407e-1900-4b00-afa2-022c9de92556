import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useEffect } from 'react';
import { Box, Text } from 'ink';
import { LoadingIndicator } from './LoadingIndicator.js';
export const LoadingScreen = ({ themeManager, status = 'Initializing Arien AI CLI...', progress = 0, showBranding = true, showProgress = true, showStatus = true, onComplete, }) => {
    const [currentStep, setCurrentStep] = useState(0);
    const [loadingSteps, setLoadingSteps] = useState([
        { id: 'init', label: 'Initializing application', status: 'loading' },
        { id: 'config', label: 'Loading configuration', status: 'pending' },
        { id: 'providers', label: 'Setting up AI providers', status: 'pending' },
        { id: 'tools', label: 'Discovering tools', status: 'pending' },
        { id: 'themes', label: 'Loading themes', status: 'pending' },
        { id: 'ready', label: 'Ready to start', status: 'pending' },
    ]);
    // Simulate loading progress
    useEffect(() => {
        const interval = setInterval(() => {
            setLoadingSteps(prev => {
                const newSteps = [...prev];
                const current = newSteps[currentStep];
                if (current && current.status === 'loading') {
                    // Complete current step
                    current.status = 'complete';
                    // Start next step
                    if (currentStep < newSteps.length - 1) {
                        newSteps[currentStep + 1].status = 'loading';
                        setCurrentStep(currentStep + 1);
                    }
                    else {
                        // All steps complete
                        setTimeout(() => {
                            onComplete?.();
                        }, 500);
                    }
                }
                return newSteps;
            });
        }, 800 + Math.random() * 400); // Random delay between 800-1200ms
        return () => clearInterval(interval);
    }, [currentStep, onComplete]);
    const renderBranding = () => {
        if (!showBranding)
            return null;
        return (_jsxs(Box, { flexDirection: "column", alignItems: "center", marginBottom: 2, children: [_jsx(Text, { children: themeManager.primary('╔══════════════════════════════════════╗') }), _jsxs(Text, { children: [themeManager.primary('║'), "          ", themeManager.accent('🤖 ARIEN AI CLI'), "          ", themeManager.primary('║')] }), _jsxs(Text, { children: [themeManager.primary('║'), "     ", themeManager.secondary('AI-Powered Terminal Assistant'), "     ", themeManager.primary('║')] }), _jsx(Text, { children: themeManager.primary('╚══════════════════════════════════════╝') })] }));
    };
    const renderLoadingSteps = () => {
        return (_jsx(Box, { flexDirection: "column", marginBottom: 1, children: loadingSteps.map((step, index) => {
                const isActive = index === currentStep;
                const isComplete = step.status === 'complete';
                const isError = step.status === 'error';
                const isPending = step.status === 'pending';
                let statusIcon = '';
                let statusColor = (text) => themeManager.muted(text);
                if (isComplete) {
                    statusIcon = themeManager.getCurrentTheme().symbols.check;
                    statusColor = (text) => themeManager.success(text);
                }
                else if (isError) {
                    statusIcon = themeManager.getCurrentTheme().symbols.cross;
                    statusColor = (text) => themeManager.error(text);
                }
                else if (isActive) {
                    statusIcon = '';
                    statusColor = (text) => themeManager.primary(text);
                }
                else if (isPending) {
                    statusIcon = '○';
                    statusColor = (text) => themeManager.muted(text);
                }
                return (_jsx(Box, { marginLeft: 2, children: _jsxs(Text, { children: [isActive ? (_jsx(LoadingIndicator, { themeManager: themeManager, text: "", type: "spinner" })) : (statusColor(statusIcon)), ' ', isActive ? statusColor(step.label) : statusColor(step.label)] }) }, step.id));
            }) }));
    };
    const renderProgressBar = () => {
        if (!showProgress)
            return null;
        const completedSteps = loadingSteps.filter(step => step.status === 'complete').length;
        const totalSteps = loadingSteps.length;
        const progressPercent = Math.round((completedSteps / totalSteps) * 100);
        const barWidth = 30;
        const filledWidth = Math.round((progressPercent / 100) * barWidth);
        const emptyWidth = barWidth - filledWidth;
        const progressBar = themeManager.success('█'.repeat(filledWidth)) +
            themeManager.muted('░'.repeat(emptyWidth));
        return (_jsx(Box, { flexDirection: "column", marginBottom: 1, children: _jsxs(Box, { marginLeft: 2, children: [_jsx(Text, { children: themeManager.secondary('Progress: ') }), _jsxs(Text, { children: ["[", progressBar, "]"] }), _jsxs(Text, { children: [" ", themeManager.accent(`${progressPercent}%`)] })] }) }));
    };
    const renderStatus = () => {
        if (!showStatus)
            return null;
        return (_jsxs(Box, { marginLeft: 2, marginBottom: 1, children: [_jsx(Text, { children: themeManager.muted('Status: ') }), _jsx(Text, { children: themeManager.info(status) })] }));
    };
    const renderTips = () => {
        const tips = [
            'Tip: Use Ctrl+H to view help and keyboard shortcuts',
            'Tip: Multiple AI providers are supported (OpenAI, Google, Anthropic, DeepSeek)',
            'Tip: Function calling enables powerful tool integration',
            'Tip: Use Ctrl+L to clear chat history',
            'Tip: Themes can be changed anytime in settings',
        ];
        const randomTip = tips[Math.floor(Math.random() * tips.length)];
        return (_jsx(Box, { marginTop: 2, marginLeft: 2, children: _jsx(Text, { children: themeManager.muted(randomTip) }) }));
    };
    return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [renderBranding(), _jsxs(Box, { flexDirection: "column", borderStyle: "round", borderColor: "blue", padding: 1, children: [renderStatus(), renderProgressBar(), renderLoadingSteps(), renderTips()] }), _jsx(Box, { marginTop: 1, justifyContent: "center", children: _jsx(Text, { children: themeManager.muted('Please wait while we prepare your AI assistant...') }) })] }));
};
//# sourceMappingURL=LoadingScreen.js.map