/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState } from 'react';
import { Box, Text, useStdout } from 'ink';
import { ThemeManager } from '../../themes/themes.js';
import { ProviderManager } from '../../providers/manager.js';
import { ToolRegistry } from '../../tools/tool-registry.js';
import { useChat } from '../../hooks/useChat.js';
import { MessageList } from './MessageList.js';
import { ChatInput } from './ChatInput.js';
import { Header } from '../ui/Header.js';
import { HelpScreen } from '../ui/HelpScreen.js';
import { getCoreSystemPrompt } from '../../system-prompt/prompts.js';

interface ChatContainerProps {
  themeManager: ThemeManager;
  providerManager: ProviderManager;
  toolRegistry: ToolRegistry;
  systemPrompt?: string;
}

export const ChatContainer: React.FC<ChatContainerProps> = ({
  themeManager,
  providerManager,
  toolRegistry,
  systemPrompt = getCoreSystemPrompt(),
}) => {
  const [showHelp, setShowHelp] = useState(false);
  const { stdout } = useStdout();

  const {
    messages,
    isLoading,
    error,
    sendMessage,
    clearMessages,
  } = useChat({
    providerManager,
    toolRegistry,
    systemPrompt,
  });

  const handleSendMessage = async (message: string) => {
    await sendMessage(message);
  };

  const handleClear = () => {
    clearMessages();
  };

  const handleHelp = () => {
    setShowHelp(true);
  };

  const handleCloseHelp = () => {
    setShowHelp(false);
  };

  const handleExit = () => {
    process.exit(0);
  };

  if (showHelp) {
    return (
      <Box flexDirection="column" padding={1}>
        <HelpScreen themeManager={themeManager} onClose={handleCloseHelp} />
      </Box>
    );
  }

  const availableHeight = (stdout.rows || 24) - 8; // Reserve space for header and input

  return (
    <Box flexDirection="column" height={stdout.rows || 24} padding={1}>
      <Header
        themeManager={themeManager}
        providerManager={providerManager}
        showProviderInfo={true}
        showControls={true}
      />
      
      {error && (
        <Box marginBottom={1}>
          <Text>{themeManager.error(`Error: ${error}`)}</Text>
          <Text>{themeManager.muted(' (Type your message to retry)')}</Text>
        </Box>
      )}
      
      <MessageList
        messages={messages}
        themeManager={themeManager}
        isLoading={isLoading}
        maxHeight={availableHeight}
      />
      
      <ChatInput
        themeManager={themeManager}
        onSendMessage={handleSendMessage}
        onClear={handleClear}
        onHelp={handleHelp}
        onExit={handleExit}
        disabled={isLoading}
      />
    </Box>
  );
};
