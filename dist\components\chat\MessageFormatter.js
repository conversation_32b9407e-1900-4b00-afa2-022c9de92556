import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
export const MessageFormatter = ({ themeManager, content, enableMarkdown = true, enableCodeHighlighting = true, enableLinkDetection = true, maxWidth = 80, }) => {
    const parseMarkdown = (text) => {
        if (!enableMarkdown) {
            return [{ type: 'text', content: text }];
        }
        const segments = [];
        const lines = text.split('\n');
        let inCodeBlock = false;
        let codeBlockLanguage = '';
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            // Code blocks
            if (line.startsWith('```')) {
                if (inCodeBlock) {
                    inCodeBlock = false;
                    codeBlockLanguage = '';
                }
                else {
                    inCodeBlock = true;
                    codeBlockLanguage = line.substring(3).trim();
                }
                continue;
            }
            if (inCodeBlock) {
                segments.push({
                    type: 'codeblock',
                    content: line,
                    language: codeBlockLanguage,
                });
                continue;
            }
            // Headers
            if (line.startsWith('#')) {
                const level = line.match(/^#+/)?.[0].length || 1;
                const headerContent = line.substring(level).trim();
                segments.push({
                    type: 'header',
                    content: headerContent,
                    level,
                });
                continue;
            }
            // Lists
            if (line.match(/^\s*[-*+]\s/)) {
                const listContent = line.replace(/^\s*[-*+]\s/, '');
                segments.push({
                    type: 'list',
                    content: listContent,
                });
                continue;
            }
            // Parse inline formatting
            const inlineSegments = parseInlineFormatting(line);
            segments.push(...inlineSegments);
            // Add line break if not the last line
            if (i < lines.length - 1) {
                segments.push({ type: 'text', content: '\n' });
            }
        }
        return segments;
    };
    const parseInlineFormatting = (text) => {
        const segments = [];
        let currentText = text;
        // Regular expressions for different formatting
        const patterns = [
            { type: 'code', regex: /`([^`]+)`/g },
            { type: 'bold', regex: /\*\*([^*]+)\*\*/g },
            { type: 'italic', regex: /\*([^*]+)\*/g },
            { type: 'link', regex: /\[([^\]]+)\]\(([^)]+)\)/g },
        ];
        const matches = [];
        // Find all matches
        patterns.forEach(pattern => {
            let match;
            while ((match = pattern.regex.exec(text)) !== null) {
                matches.push({
                    type: pattern.type,
                    start: match.index,
                    end: match.index + match[0].length,
                    content: match[1],
                    url: pattern.type === 'link' ? match[2] : undefined,
                });
            }
        });
        // Sort matches by position
        matches.sort((a, b) => a.start - b.start);
        let lastEnd = 0;
        matches.forEach(match => {
            // Add text before the match
            if (match.start > lastEnd) {
                const beforeText = text.substring(lastEnd, match.start);
                if (beforeText) {
                    segments.push({ type: 'text', content: beforeText });
                }
            }
            // Add the formatted segment
            segments.push({
                type: match.type,
                content: match.content,
            });
            lastEnd = match.end;
        });
        // Add remaining text
        if (lastEnd < text.length) {
            const remainingText = text.substring(lastEnd);
            if (remainingText) {
                segments.push({ type: 'text', content: remainingText });
            }
        }
        // If no matches found, return the original text
        if (matches.length === 0) {
            segments.push({ type: 'text', content: text });
        }
        return segments;
    };
    const detectLinks = (text) => {
        if (!enableLinkDetection)
            return text;
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        return text.replace(urlRegex, (url) => `[${url}](${url})`);
    };
    const wrapText = (text, width) => {
        const words = text.split(' ');
        const lines = [];
        let currentLine = '';
        words.forEach(word => {
            if (currentLine.length + word.length + 1 <= width) {
                currentLine += (currentLine ? ' ' : '') + word;
            }
            else {
                if (currentLine)
                    lines.push(currentLine);
                currentLine = word;
            }
        });
        if (currentLine)
            lines.push(currentLine);
        return lines;
    };
    const renderSegment = (segment, index) => {
        switch (segment.type) {
            case 'text':
                return _jsx(Text, { children: segment.content }, index);
            case 'bold':
                return _jsx(Text, { children: themeManager.primary(segment.content) }, index);
            case 'italic':
                return _jsx(Text, { children: themeManager.secondary(segment.content) }, index);
            case 'code':
                return _jsx(Text, { children: themeManager.accent(`\`${segment.content}\``) }, index);
            case 'codeblock':
                const language = segment.language || 'text';
                return (_jsx(Box, { marginLeft: 2, borderStyle: "single", borderColor: "gray", children: _jsxs(Box, { flexDirection: "column", padding: 1, children: [language && (_jsx(Text, { children: themeManager.muted(`// ${language}`) })), _jsx(Text, { children: themeManager.accent(segment.content) })] }) }, index));
            case 'header':
                const headerColor = segment.level === 1 ? themeManager.primary :
                    segment.level === 2 ? themeManager.secondary :
                        themeManager.accent;
                const headerPrefix = '#'.repeat(segment.level || 1);
                return (_jsx(Text, { children: headerColor(`${headerPrefix} ${segment.content}`) }, index));
            case 'list':
                return (_jsx(Box, { marginLeft: 2, children: _jsxs(Text, { children: [themeManager.accent('•'), " ", segment.content] }) }, index));
            case 'link':
                return _jsx(Text, { children: themeManager.info(segment.content) }, index);
            default:
                return _jsx(Text, { children: segment.content }, index);
        }
    };
    const processedContent = enableLinkDetection ? detectLinks(content) : content;
    const segments = parseMarkdown(processedContent);
    return (_jsx(Box, { flexDirection: "column", width: maxWidth, children: segments.map((segment, index) => renderSegment(segment, index)) }));
};
// Specialized formatter variants
export const CodeFormatter = (props) => (_jsx(MessageFormatter, { ...props, enableMarkdown: false, enableCodeHighlighting: true }));
export const PlainTextFormatter = (props) => (_jsx(MessageFormatter, { ...props, enableMarkdown: false, enableCodeHighlighting: false, enableLinkDetection: false }));
export const MarkdownFormatter = (props) => (_jsx(MessageFormatter, { ...props, enableMarkdown: true }));
//# sourceMappingURL=MessageFormatter.js.map