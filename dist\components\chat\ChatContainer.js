import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState } from 'react';
import { Box, Text, useStdout } from 'ink';
import { useChat } from '../../hooks/useChat.js';
import { MessageList } from './MessageList.js';
import { ChatInput } from './ChatInput.js';
import { Header } from '../ui/Header.js';
import { HelpScreen } from '../ui/HelpScreen.js';
import { getCoreSystemPrompt } from '../../system-prompt/prompts.js';
export const ChatContainer = ({ themeManager, providerManager, toolRegistry, systemPrompt = getCoreSystemPrompt(), }) => {
    const [showHelp, setShowHelp] = useState(false);
    const { stdout } = useStdout();
    const { messages, isLoading, error, sendMessage, clearMessages, } = useChat({
        providerManager,
        toolRegistry,
        systemPrompt,
    });
    const handleSendMessage = async (message) => {
        await sendMessage(message);
    };
    const handleClear = () => {
        clearMessages();
    };
    const handleHelp = () => {
        setShowHelp(true);
    };
    const handleCloseHelp = () => {
        setShowHelp(false);
    };
    const handleExit = () => {
        process.exit(0);
    };
    if (showHelp) {
        return (_jsx(Box, { flexDirection: "column", padding: 1, children: _jsx(HelpScreen, { themeManager: themeManager, onClose: handleCloseHelp }) }));
    }
    const availableHeight = (stdout.rows || 24) - 8; // Reserve space for header and input
    return (_jsxs(Box, { flexDirection: "column", height: stdout.rows || 24, padding: 1, children: [_jsx(Header, { themeManager: themeManager, providerManager: providerManager, showProviderInfo: true, showControls: true }), error && (_jsxs(Box, { marginBottom: 1, children: [_jsx(Text, { children: themeManager.error(`Error: ${error}`) }), _jsx(Text, { children: themeManager.muted(' (Type your message to retry)') })] })), _jsx(MessageList, { messages: messages, themeManager: themeManager, isLoading: isLoading, maxHeight: availableHeight }), _jsx(ChatInput, { themeManager: themeManager, onSendMessage: handleSendMessage, onClear: handleClear, onHelp: handleHelp, onExit: handleExit, disabled: isLoading })] }));
};
//# sourceMappingURL=ChatContainer.js.map