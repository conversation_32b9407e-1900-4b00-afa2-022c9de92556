{"version": 3, "file": "StatusBar.d.ts", "sourceRoot": "", "sources": ["../../../src/components/ui/StatusBar.tsx"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,MAAM,OAAO,CAAC;AAE1B,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAEtD,UAAU,cAAc;IACtB,YAAY,EAAE,YAAY,CAAC;IAC3B,SAAS,CAAC,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC;IAC9B,WAAW,CAAC,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC;IAChC,UAAU,CAAC,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC;IAC/B,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,gBAAgB,CAAC,EAAE,WAAW,GAAG,cAAc,GAAG,YAAY,CAAC;IAC/D,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,gBAAgB,CAAC,EAAE,OAAO,CAAC;CAC5B;AAED,eAAO,MAAM,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC,cAAc,CA+I9C,CAAC"}